<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Config</title>
</head>
<body>
    <button id="test-button">Teste</button>
    
    <script>
        console.log('=== TESTE DE CONFIGURAÇÃO ===');
        
        // Teste simples da classe ConfigManager
        class TestConfigManager {
            constructor() {
                console.log('TestConfigManager construtor chamado');
                this.init();
            }
            
            init() {
                console.log('TestConfigManager init chamado');
                this.bindEvents();
            }
            
            bindEvents() {
                console.log('TestConfigManager bindEvents chamado');
                const button = document.getElementById('test-button');
                if (button) {
                    button.addEventListener('click', () => {
                        console.log('Botão clicado!');
                        alert('Funcionou!');
                    });
                    console.log('Event listener adicionado');
                }
            }
        }
        
        // Inicializar quando DOM estiver pronto
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM carregado, inicializando TestConfigManager...');
            const testManager = new TestConfigManager();
            console.log('TestConfigManager inicializado:', testManager);
        });
        
        console.log('Script carregado');
    </script>
</body>
</html>
