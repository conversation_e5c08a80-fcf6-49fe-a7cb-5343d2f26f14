<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Simples Config</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .config-button {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #007bff;
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .links-section {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .link-button {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: white;
            border: 2px solid transparent;
            border-radius: 10px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }
        
        .link-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .button-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .button-content i {
            font-size: 1.5rem;
            width: 30px;
            text-align: center;
        }
        
        .button-text {
            flex: 1;
        }
        
        .button-text strong {
            display: block;
            font-size: 1.1rem;
        }
        
        .button-text small {
            color: #666;
            font-size: 0.9rem;
        }
        
        .config-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }
        
        .config-modal.active {
            display: flex;
        }
        
        .config-modal-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <!-- Botão de Configurações -->
    <button class="config-button" id="config-button" title="Configurações">
        <i class="fas fa-cog"></i>
    </button>

    <!-- Container principal -->
    <div class="container">
        <h1>Teste Simples - Estúdio730</h1>
        
        <!-- Seção de Links -->
        <div class="links-section">
            <!-- Links serão renderizados aqui -->
        </div>
    </div>

    <!-- Modal de Configurações -->
    <div class="config-modal" id="config-modal">
        <div class="config-modal-content">
            <h2>Configurações</h2>
            <p>Modal de configurações funcionando!</p>
            <button onclick="document.getElementById('config-modal').classList.remove('active'); document.body.style.overflow = '';">Fechar</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="config-test-simple.js"></script>
</body>
</html>
