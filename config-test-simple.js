// Teste simplificado do ConfigManager
class SimpleConfigManager {
    constructor() {
        console.log('SimpleConfigManager: Construtor iniciado');
        this.storageKey = 'estudio730_config';
        this.defaultConfig = this.getDefaultConfig();
        console.log('SimpleConfigManager: defaultConfig criado');
        this.currentConfig = this.loadConfig();
        console.log('SimpleConfigManager: currentConfig carregado');
        this.init();
        console.log('SimpleConfigManager: init chamado');
    }

    getDefaultConfig() {
        console.log('SimpleConfigManager: getDefaultConfig chamado');
        return {
            links: [
                {
                    id: 'whatsapp',
                    name: 'WhatsApp',
                    url: 'https://wa.me/5511999999999',
                    icon: 'fab fa-whatsapp',
                    color: '#25d366',
                    visible: true,
                    removable: false,
                    order: 1
                }
            ],
            settings: {
                lastModified: Date.now(),
                version: '1.0.0'
            }
        };
    }

    loadConfig() {
        console.log('SimpleConfigManager: loadConfig chamado');
        try {
            const saved = localStorage.getItem(this.storageKey);
            if (saved) {
                const config = JSON.parse(saved);
                return this.mergeConfigs(this.defaultConfig, config);
            }
        } catch (error) {
            console.warn('Erro ao carregar configurações:', error);
        }
        return { ...this.defaultConfig };
    }

    mergeConfigs(defaultConfig, userConfig) {
        console.log('SimpleConfigManager: mergeConfigs chamado');
        const merged = { ...defaultConfig };
        
        if (userConfig.links) {
            const defaultLinks = defaultConfig.links;
            const userLinks = userConfig.links;
            
            merged.links = defaultLinks.map(defaultLink => {
                const userLink = userLinks.find(ul => ul.id === defaultLink.id);
                return userLink ? { ...defaultLink, ...userLink } : defaultLink;
            });

            const customLinks = userLinks.filter(ul => !defaultLinks.some(dl => dl.id === ul.id));
            merged.links.push(...customLinks);
        }

        if (userConfig.settings) {
            merged.settings = { ...merged.settings, ...userConfig.settings };
        }

        return merged;
    }

    init() {
        console.log('SimpleConfigManager: init chamado');
        this.bindEvents();
        console.log('SimpleConfigManager: bindEvents concluído');
        this.renderLinks();
        console.log('SimpleConfigManager: renderLinks concluído');
        console.log('SimpleConfigManager: inicialização completa');
    }

    bindEvents() {
        console.log('SimpleConfigManager: bindEvents chamado');
        const configButton = document.getElementById('config-button');
        if (configButton) {
            configButton.addEventListener('click', () => this.openModal());
            console.log('SimpleConfigManager: Event listener adicionado ao botão');
        } else {
            console.warn('SimpleConfigManager: Botão de configuração não encontrado');
        }
    }

    renderLinks() {
        console.log('SimpleConfigManager: renderLinks chamado');
        const linksSection = document.querySelector('.links-section');
        if (!linksSection) {
            console.warn('SimpleConfigManager: .links-section não encontrada');
            return;
        }

        const existingButtons = linksSection.querySelectorAll('.link-button');
        existingButtons.forEach(btn => btn.remove());

        const visibleLinks = this.currentConfig.links
            .filter(link => link.visible)
            .sort((a, b) => a.order - b.order);

        visibleLinks.forEach(link => {
            const linkElement = this.createLinkElement(link);
            linksSection.appendChild(linkElement);
        });
        
        console.log('SimpleConfigManager: renderLinks concluído');
    }

    createLinkElement(link) {
        console.log('SimpleConfigManager: createLinkElement chamado para', link.name);
        const linkEl = document.createElement('a');
        linkEl.href = link.url;
        linkEl.className = `link-button custom-link`;
        linkEl.target = '_blank';
        linkEl.rel = 'noopener noreferrer';
        linkEl.style.setProperty('--link-color', link.color);

        linkEl.innerHTML = `
            <div class="button-content">
                <i class="${link.icon}"></i>
                <span class="button-text">
                    <strong>${link.name}</strong>
                    <small>Clique para acessar</small>
                </span>
            </div>
            <i class="fas fa-chevron-right arrow"></i>
        `;

        return linkEl;
    }

    openModal() {
        console.log('SimpleConfigManager: openModal chamado');
        const modal = document.getElementById('config-modal');
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
            console.log('SimpleConfigManager: Modal aberto');
        } else {
            console.warn('SimpleConfigManager: Modal não encontrado');
        }
    }
}

// Inicializar quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    console.log('SimpleConfigManager: DOM carregado, inicializando...');
    try {
        window.simpleConfigManager = new SimpleConfigManager();
        console.log('SimpleConfigManager: Inicializado com sucesso');
    } catch (error) {
        console.error('SimpleConfigManager: Erro na inicialização:', error);
        console.error('Stack trace:', error.stack);
    }
});
